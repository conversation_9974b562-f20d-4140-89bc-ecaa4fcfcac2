package database

import (
	"fmt"
	"hospital-management/internal/config"
	"hospital-management/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Init(cfg *config.Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=Asia/Shanghai",
		cfg.DBHost, cfg.DBUser, cfg.DBPassword, cfg.DBName, cfg.DBPort, cfg.DBSSLMode)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return db, nil
}

func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		// 基础模型
		&models.Department{},
		&models.User{},
		&models.Role{},
		&models.UserRole{},
		&models.File{},
		
		// 审批流模型
		&models.ApprovalFlow{},
		&models.ApprovalNode{},
		
		// 预算管理模型
		&models.BudgetScheme{},
		&models.BudgetSubject{},
		&models.BudgetItem{},
		
		// 支出控制模型
		&models.PreApplication{},
		&models.ExpenseApplication{},
		&models.ExpenseDetail{},
		&models.Payment{},
		
		// 采购管理模型
		&models.Supplier{},
		&models.PurchaseRequisition{},
		
		// 合同管理模型
		&models.Contract{},
		&models.ContractPaymentSchedule{},
		
		// 资产管理模型
		&models.AssetCategory{},
		&models.Asset{},
	)
}
